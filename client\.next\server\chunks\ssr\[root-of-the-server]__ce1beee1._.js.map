{"version": 3, "sources": [], "sections": [{"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/utils/auth.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\n// Interface untuk user data\nexport interface User {\n  id: number;\n  username: string;\n  role: 'admin';\n  posyandu_name: string;\n}\n\n// Interface untuk response login\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  data: {\n    user: User;\n    tokens: {\n      accessToken: string;\n      refreshToken: string;\n      expiresIn: string;\n    };\n  };\n}\n\n// Konfigurasi axios dengan interceptor\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n});\n\n// Request interceptor untuk menambahkan token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = getAccessToken();\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor untuk handle token expired\napiClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    if (error.response?.status === 401) {\n      // Token expired atau invalid\n      clearAuthData();\n      \n      // Redirect ke login jika bukan di halaman login\n      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {\n        window.location.href = '/login';\n      }\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Fungsi untuk mendapatkan access token\nexport const getAccessToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem('accessToken');\n};\n\n// Fungsi untuk mendapatkan refresh token\nexport const getRefreshToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem('refreshToken');\n};\n\n// Fungsi untuk mendapatkan user data\nexport const getUser = (): User | null => {\n  if (typeof window === 'undefined') return null;\n  const userStr = localStorage.getItem('user');\n  return userStr ? JSON.parse(userStr) : null;\n};\n\n// Fungsi untuk menyimpan auth data\nexport const setAuthData = (tokens: { accessToken: string; refreshToken: string }, user: User) => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.setItem('accessToken', tokens.accessToken);\n  localStorage.setItem('refreshToken', tokens.refreshToken);\n  localStorage.setItem('user', JSON.stringify(user));\n};\n\n// Fungsi untuk menghapus auth data\nexport const clearAuthData = () => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.removeItem('accessToken');\n  localStorage.removeItem('refreshToken');\n  localStorage.removeItem('user');\n};\n\n// Fungsi untuk cek apakah user sudah login\nexport const isAuthenticated = (): boolean => {\n  return !!getAccessToken() && !!getUser();\n};\n\n// Fungsi untuk cek apakah user adalah admin\nexport const isAdmin = (): boolean => {\n  const user = getUser();\n  return user?.role === 'admin';\n};\n\n\n\n// Fungsi login dengan username/password\nexport const loginWithCredentials = async (\n  username: string, \n  password: string, \n  rememberMe: boolean = false\n): Promise<LoginResponse> => {\n  const response = await apiClient.post('/auth/login', {\n    username,\n    password,\n    rememberMe,\n    deviceInfo: navigator.userAgent\n  });\n  \n  if (response.data.success) {\n    setAuthData(response.data.data.tokens, response.data.data.user);\n  }\n  \n  return response.data;\n};\n\n// Fungsi login dengan PIN\nexport const loginWithPin = async (\n  pin: string, \n  rememberMe: boolean = false\n): Promise<LoginResponse> => {\n  const response = await apiClient.post('/auth/login-pin', {\n    pin,\n    rememberMe,\n    deviceInfo: navigator.userAgent\n  });\n  \n  if (response.data.success) {\n    setAuthData(response.data.data.tokens, response.data.data.user);\n  }\n  \n  return response.data;\n};\n\n// Fungsi logout\nexport const logout = async (): Promise<void> => {\n  try {\n    await apiClient.post('/auth/logout');\n  } catch (error) {\n    console.error('Logout error:', error);\n  } finally {\n    clearAuthData();\n  }\n};\n\n// Fungsi logout dari semua device\nexport const logoutAll = async (): Promise<void> => {\n  try {\n    await apiClient.post('/auth/logout-all');\n  } catch (error) {\n    console.error('Logout all error:', error);\n  } finally {\n    clearAuthData();\n  }\n};\n\n// Fungsi untuk mendapatkan info user yang sedang login\nexport const getCurrentUser = async (): Promise<User> => {\n  const response = await apiClient.get('/auth/me');\n  return response.data.data.user;\n};\n\n// Fungsi untuk verifikasi token\nexport const verifyToken = async (): Promise<boolean> => {\n  try {\n    await apiClient.get('/auth/verify');\n    return true;\n  } catch (error) {\n    return false;\n  }\n};\n\n// Export axios client untuk digunakan di komponen lain\nexport { apiClient };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAEA,MAAM,eAAe;AAwBrB,uCAAuC;AACvC,MAAM,YAAY,wLAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kDAAkD;AAClD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,OAAO;IACL,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,6BAA6B;QAC7B;QAEA,gDAAgD;QAChD,IAAI,gBAAkB,eAAe,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;;IAG1E;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,kBAAkB;IAC7B,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,UAAU;IACrB,wCAAmC,OAAO;;;IAC1C,MAAM;AAER;AAGO,MAAM,cAAc,CAAC,QAAuD;IACjF,wCAAmC;;;AAKrC;AAGO,MAAM,gBAAgB;IAC3B,wCAAmC;;;AAKrC;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC;AACjC;AAGO,MAAM,UAAU;IACrB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;AAKO,MAAM,uBAAuB,OAClC,UACA,UACA,aAAsB,KAAK;IAE3B,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,eAAe;QACnD;QACA;QACA;QACA,YAAY,UAAU,SAAS;IACjC;IAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;QACzB,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;IAChE;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,eAAe,OAC1B,KACA,aAAsB,KAAK;IAE3B,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB;QACvD;QACA;QACA,YAAY,UAAU,SAAS;IACjC;IAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;QACzB,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;IAChE;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,SAAS;IACpB,IAAI;QACF,MAAM,UAAU,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;IACjC,SAAU;QACR;IACF;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,UAAU,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;IACrC,SAAU;QACR;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC;IACrC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;AAChC;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { isAuthenticated, isAdmin, getUser, verifyToken } from '@/utils/auth';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAdmin?: boolean;\n  fallbackPath?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallbackPath = '/login' \n}: ProtectedRouteProps) {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthorized, setIsAuthorized] = useState(false);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        // Cek apakah user sudah login\n        if (!isAuthenticated()) {\n          router.push(fallbackPath);\n          return;\n        }\n\n        // Verifikasi token dengan server\n        const isTokenValid = await verifyToken();\n        if (!isTokenValid) {\n          router.push(fallbackPath);\n          return;\n        }\n\n        // Cek role jika diperlukan\n        if (requireAdmin && !isAdmin()) {\n          router.push('/unauthorized');\n          return;\n        }\n\n        setIsAuthorized(true);\n      } catch (error) {\n        console.error('Auth check error:', error);\n        router.push(fallbackPath);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [router, requireAdmin, fallbackPath]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4\">\n            <svg className=\"animate-spin w-8 h-8 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Memverifikasi akses...</h3>\n          <p className=\"text-gray-600\">Mohon tunggu sebentar</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthorized) {\n    return null; // Router akan redirect\n  }\n\n  return <>{children}</>;\n}\n\n// HOC untuk proteksi halaman\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  requireAdmin: boolean = false\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook untuk mendapatkan user info\nexport function useAuth() {\n  const [user, setUser] = useState(getUser());\n  const [isLoading, setIsLoading] = useState(false);\n\n  const refreshUser = async () => {\n    setIsLoading(true);\n    try {\n      const currentUser = getUser();\n      setUser(currentUser);\n    } catch (error) {\n      console.error('Error refreshing user:', error);\n      setUser(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    refreshUser();\n  }, []);\n\n  return {\n    user,\n    isLoading,\n    isAuthenticated: isAuthenticated(),\n    isAdmin: isAdmin(),\n    refreshUser\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,eAAe,EACrC,QAAQ,EACR,eAAe,KAAK,EACpB,eAAe,QAAQ,EACH;IACpB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,KAAK;oBACtB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,iCAAiC;gBACjC,MAAM,eAAe,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;gBACrC,IAAI,CAAC,cAAc;oBACjB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,gBAAgB,CAAC,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD,KAAK;oBAC9B,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,gBAAgB;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAQ;QAAc;KAAa;IAEvC,IAAI,WAAW;QACb,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;4BAAqC,MAAK;4BAAO,SAAQ;;8CACtE,6WAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,6WAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;;kCAGvD,6WAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6WAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,MAAM,uBAAuB;IACtC;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,SACd,SAAiC,EACjC,eAAwB,KAAK;IAE7B,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,6WAAC;YAAe,cAAc;sBAC5B,cAAA,6WAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc;QAClB,aAAa;QACb,IAAI;YACF,MAAM,cAAc,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;YAC1B,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,QAAQ;QACV,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,iBAAiB,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;QAC/B,SAAS,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/components/AdminSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useAuth } from '@/components/ProtectedRoute';\nimport { logout } from '@/utils/auth';\nimport { useRouter } from 'next/navigation';\n\ninterface SidebarProps {\n  isOpen: boolean;\n  setIsOpen: (isOpen: boolean) => void;\n}\n\nexport default function AdminSidebar({ isOpen, setIsOpen }: SidebarProps) {\n  const pathname = usePathname();\n  const { user } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n      router.push('/');\n    }\n  };\n\n  const menuItems = [\n    {\n      name: 'Dashboard',\n      href: '/admin',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h10a2 2 0 012 2v0a2 2 0 002 2v0a2 2 0 00-2 2H5a2 2 0 00-2-2v0z\" />\n        </svg>\n      ),\n      badge: null\n    },\n    {\n      name: 'Daftar Lansia',\n      href: '/admin/profiles',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n      badge: null\n    },\n    {\n      name: 'Daftar Baru',\n      href: '/form',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n        </svg>\n      ),\n      badge: 'New'\n    },\n    {\n      name: 'Scan QR Code',\n      href: '/scan',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\" />\n        </svg>\n      ),\n      badge: null\n    }\n  ];\n\n  const quickActions = [\n    {\n      name: 'Beranda Publik',\n      href: '/',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 bg-gray-900 bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed top-0 left-0 z-50 h-screen w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out\n        lg:translate-x-0 lg:fixed lg:inset-y-0\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        {/* Header */}\n        <div className=\"flex items-center justify-between h-16 px-6 bg-blue-600\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-white rounded-lg flex items-center justify-center\">\n              <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </div>\n            <div className=\"text-white\">\n              <h1 className=\"text-sm font-bold\">Kesehatan Lansia</h1>\n              <p className=\"text-xs text-blue-100\">Admin Panel</p>\n            </div>\n          </div>\n          <button\n            onClick={() => setIsOpen(false)}\n            className=\"lg:hidden text-white hover:text-blue-200 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* User Info */}\n        <div className=\"px-6 py-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">\n                {user?.username?.charAt(0).toUpperCase() || 'A'}\n              </span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-semibold text-gray-900 truncate\">\n                {user?.username || 'admin'}\n              </p>\n              <p className=\"text-xs text-gray-500 truncate\">\n                {user?.posyandu_name || 'Posyandu Melati'}\n              </p>\n            </div>\n            <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2\">\n          <div className=\"space-y-1\">\n            {menuItems.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={`\n                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\n                    ${isActive\n                      ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <span className={`\n                    mr-3 transition-colors duration-200\n                    ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'}\n                  `}>\n                    {item.icon}\n                  </span>\n                  <span className=\"flex-1\">{item.name}</span>\n                  {item.badge && (\n                    <span className=\"ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\n                      {item.badge}\n                    </span>\n                  )}\n                  {isActive && (\n                    <div className=\"ml-2 w-1.5 h-1.5 bg-blue-600 rounded-full\"></div>\n                  )}\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"pt-6 mt-6 border-t border-gray-200\">\n            <h3 className=\"px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3\">\n              Aksi Cepat\n            </h3>\n            <div className=\"space-y-1\">\n              {quickActions.map((action) => (\n                <Link\n                  key={action.name}\n                  href={action.href}\n                  onClick={() => setIsOpen(false)}\n                  className=\"group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-all duration-200\"\n                >\n                  <span className=\"mr-3 text-gray-400 group-hover:text-gray-600 transition-colors duration-200\">\n                    {action.icon}\n                  </span>\n                  {action.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        </nav>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <button\n            onClick={handleLogout}\n            className=\"w-full flex items-center px-3 py-2.5 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-all duration-200 group\"\n          >\n            <svg className=\"w-5 h-5 mr-3 text-red-500 group-hover:text-red-600 transition-colors duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n            Logout\n          </button>\n          \n          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n            <p className=\"text-xs text-gray-500 text-center\">\n              © 2025 Kesehatan Lansia\n            </p>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS,aAAa,EAAE,MAAM,EAAE,SAAS,EAAgB;IACtE,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,oHAAA,CAAA,SAAM,AAAD;YACX,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6WAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6WAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6WAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6WAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6WAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6WAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6WAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6WAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6WAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6WAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE;;YAEG,wBACC,6WAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,6WAAC;gBAAI,WAAW,CAAC;;;QAGf,EAAE,SAAS,kBAAkB,oBAAoB;MACnD,CAAC;;kCAEC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6WAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6WAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,6WAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6WAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAK,WAAU;kDACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;8CAGhD,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAE,WAAU;sDACV,MAAM,YAAY;;;;;;sDAErB,6WAAC;4CAAE,WAAU;sDACV,MAAM,iBAAiB;;;;;;;;;;;;8CAG5B,6WAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC;;oBAEV,EAAE,WACE,wDACA,qDACH;kBACH,CAAC;;0DAED,6WAAC;gDAAK,WAAW,CAAC;;oBAEhB,EAAE,WAAW,kBAAkB,0CAA0C;kBAC3E,CAAC;0DACE,KAAK,IAAI;;;;;;0DAEZ,6WAAC;gDAAK,WAAU;0DAAU,KAAK,IAAI;;;;;;4CAClC,KAAK,KAAK,kBACT,6WAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAGd,0BACC,6WAAC;gDAAI,WAAU;;;;;;;uCAxBZ,KAAK,IAAI;;;;;gCA4BpB;;;;;;0CAIF,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAAyE;;;;;;kDAGvF,6WAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6WAAC,2RAAA,CAAA,UAAI;gDAEH,MAAM,OAAO,IAAI;gDACjB,SAAS,IAAM,UAAU;gDACzB,WAAU;;kEAEV,6WAAC;wDAAK,WAAU;kEACb,OAAO,IAAI;;;;;;oDAEb,OAAO,IAAI;;+CARP,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAgB1B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6WAAC;wCAAI,WAAU;wCAAoF,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC3I,cAAA,6WAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAIR,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport AdminSidebar from './AdminSidebar';\nimport { useAuth } from './ProtectedRoute';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n}\n\nexport default function AdminLayout({ children, title, subtitle }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Sidebar */}\n      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col lg:ml-64\">\n        {/* Top Header */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-16\">\n              {/* Mobile menu button */}\n              <div className=\"flex items-center\">\n                <button\n                  type=\"button\"\n                  className=\"lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors duration-200\"\n                  onClick={() => setSidebarOpen(true)}\n                >\n                  <span className=\"sr-only\">Open sidebar</span>\n                  <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  </svg>\n                </button>\n\n                {/* Page Title */}\n                <div className=\"ml-4 lg:ml-0\">\n                  {title && (\n                    <div>\n                      <h1 className=\"text-xl font-semibold text-gray-900\">{title}</h1>\n                      {subtitle && (\n                        <p className=\"text-sm text-gray-500\">{subtitle}</p>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Right side */}\n              <div className=\"flex items-center space-x-4\">\n                {/* Notifications */}\n                <button className=\"relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors duration-200\">\n                  <span className=\"sr-only\">View notifications</span>\n                  <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z\" />\n                  </svg>\n                  {/* Notification badge */}\n                  <span className=\"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400\"></span>\n                </button>\n\n                {/* User menu */}\n                <div className=\"relative\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"hidden md:block text-right\">\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.posyandu_name}</p>\n                    </div>\n                    <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">\n                        {user?.username?.charAt(0).toUpperCase() || 'A'}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAoB;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC,kIAAA,CAAA,UAAY;gBAAC,QAAQ;gBAAa,WAAW;;;;;;0BAG9C,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAO,WAAU;kCAChB,cAAA,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDAEb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;;kEAE9B,6WAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6WAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6WAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;0DAKzE,6WAAC;gDAAI,WAAU;0DACZ,uBACC,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;sEAAuC;;;;;;wDACpD,0BACC,6WAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;kDAQhD,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC;gDAAO,WAAU;;kEAChB,6WAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6WAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6WAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAGvE,6WAAC;wDAAK,WAAU;;;;;;;;;;;;0DAIlB,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAE,WAAU;8EAAqC,MAAM;;;;;;8EACxD,6WAAC;oEAAE,WAAU;8EAAyB,MAAM;;;;;;;;;;;;sEAE9C,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAK,WAAU;0EACb,MAAM,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW5D,6WAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport ProtectedRoute from '@/components/ProtectedRoute';\nimport AdminLayout from '@/components/AdminLayout';\nimport { useAuth } from '@/components/ProtectedRoute';\nimport { logout, apiClient } from '@/utils/auth';\n\ninterface DashboardStats {\n  totalLansia: number;\n  totalPemeriksaan: number;\n  lansiaAktif: number;\n  pemeriksaanBulanIni: number;\n}\n\nexport default function AdminDashboard() {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalLansia: 0,\n    totalPemeriksaan: 0,\n    lansiaAktif: 0,\n    pemeriksaanBulanIni: 0\n  });\n  const [isLoading, setIsLoading] = useState(true);\n  const [recentProfiles, setRecentProfiles] = useState<any[]>([]);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      \n      // Fetch semua profil untuk statistik\n      const profilesResponse = await apiClient.get('/profiles');\n      const profiles = profilesResponse.data.profiles || [];\n      \n      // Hitung statistik\n      const totalLansia = profiles.length;\n      const totalPemeriksaan = profiles.reduce((sum: number, profile: any) => sum + (profile.total_checkups || 0), 0);\n      const lansiaAktif = profiles.filter((profile: any) => {\n        const lastCheckup = new Date(profile.last_checkup);\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        return lastCheckup > thirtyDaysAgo;\n      }).length;\n      \n      const currentMonth = new Date().getMonth();\n      const currentYear = new Date().getFullYear();\n      const pemeriksaanBulanIni = profiles.reduce((sum: number, profile: any) => {\n        if (profile.last_checkup) {\n          const checkupDate = new Date(profile.last_checkup);\n          if (checkupDate.getMonth() === currentMonth && checkupDate.getFullYear() === currentYear) {\n            return sum + 1;\n          }\n        }\n        return sum;\n      }, 0);\n\n      setStats({\n        totalLansia,\n        totalPemeriksaan,\n        lansiaAktif,\n        pemeriksaanBulanIni\n      });\n\n      // Set recent profiles (semua data untuk scroll)\n      setRecentProfiles(profiles);\n      \n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n      router.push('/');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'short',\n      year: 'numeric'\n    });\n  };\n\n  return (\n    <ProtectedRoute requireAdmin={true}>\n      <AdminLayout\n        title=\"Dashboard Admin\"\n        subtitle={`Selamat datang, ${user?.username} - ${user?.posyandu_name}`}\n      >\n        <div className=\"p-6 space-y-6\">\n          {/* Welcome Banner */}\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl shadow-lg p-6 text-white relative overflow-hidden\">\n            <div className=\"absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full transform translate-x-8 -translate-y-8\"></div>\n            <div className=\"relative z-10\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\">\n                    <span className=\"text-2xl\">👋</span>\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl font-bold mb-1\">Selamat Datang, {user?.username}!</h2>\n                    <p className=\"text-blue-100 text-sm\">Kelola data kesehatan lansia {user?.posyandu_name} dengan mudah</p>\n                    <div className=\"flex items-center space-x-4 mt-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                        <span className=\"text-xs text-blue-100\">Online</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <svg className=\"w-3 h-3 text-blue-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        <span className=\"text-xs text-blue-100\">{new Date().toLocaleDateString('id-ID', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Lansia</p>\n                  <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                    {isLoading ? (\n                      <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                    ) : (\n                      stats.totalLansia\n                    )}\n                  </p>\n                  <p className=\"text-xs text-gray-500 mt-1\">Terdaftar di sistem</p>\n                </div>\n                <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Pemeriksaan</p>\n                  <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                    {isLoading ? (\n                      <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                    ) : (\n                      stats.totalPemeriksaan\n                    )}\n                  </p>\n                  <p className=\"text-xs text-gray-500 mt-1\">Riwayat kesehatan</p>\n                </div>\n                <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Lansia Aktif</p>\n                  <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                    {isLoading ? (\n                      <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                    ) : (\n                      stats.lansiaAktif\n                    )}\n                  </p>\n                  <p className=\"text-xs text-gray-500 mt-1\">30 hari terakhir</p>\n                </div>\n                <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Bulan Ini</p>\n                  <p className=\"text-2xl font-bold text-gray-900 mt-1\">\n                    {isLoading ? (\n                      <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                    ) : (\n                      stats.pemeriksaanBulanIni\n                    )}\n                  </p>\n                  <p className=\"text-xs text-gray-500 mt-1\">Pemeriksaan baru</p>\n                </div>\n                <div className=\"w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions & Recent Activity */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 items-start\">\n            {/* Quick Actions */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 h-full\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h3 className=\"text-lg font-bold text-gray-900\">Aksi Cepat</h3>\n                  <div className=\"w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-3 h-3 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  <Link\n                    href=\"/admin/profiles\"\n                    className=\"group flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-blue-50 transition-all duration-200\"\n                  >\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-3\">\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                      </svg>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 text-center\">Daftar Lansia</span>\n                    <span className=\"text-xs text-gray-500 text-center mt-1\">Kelola data</span>\n                  </Link>\n\n                  <Link\n                    href=\"/scan\"\n                    className=\"group flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-green-50 transition-all duration-200\"\n                  >\n                    <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-3\">\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\" />\n                      </svg>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 text-center\">Scan QR Code</span>\n                    <span className=\"text-xs text-gray-500 text-center mt-1\">Akses cepat</span>\n                  </Link>\n\n                  <Link\n                    href=\"/form\"\n                    className=\"group flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-purple-50 transition-all duration-200\"\n                  >\n                    <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-3\">\n                      <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                      </svg>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 text-center\">Daftar Baru</span>\n                    <span className=\"text-xs text-gray-500 text-center mt-1\">Tambah lansia</span>\n                  </Link>\n\n                  <Link\n                    href=\"/\"\n                    className=\"group flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200\"\n                  >\n                    <div className=\"w-12 h-12 bg-gray-200 rounded-xl flex items-center justify-center mb-3\">\n                      <svg className=\"w-6 h-6 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                      </svg>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 text-center\">Beranda</span>\n                    <span className=\"text-xs text-gray-500 text-center mt-1\">Halaman utama</span>\n                  </Link>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6 h-full flex flex-col\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-bold text-gray-900\">Aktivitas Terbaru</h3>\n                <Link\n                  href=\"/admin/profiles\"\n                  className=\"text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\"\n                >\n                  Lihat Semua →\n                </Link>\n              </div>\n              <div className=\"flex-1\">\n                {/* Container dengan tinggi tetap untuk 4 item */}\n                <div\n                  className=\"space-y-3 pr-2 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\"\n                  style={{ height: '320px' }} // Tinggi untuk 4 item (80px per item)\n                >\n                  {isLoading ? (\n                    <div className=\"space-y-3\">\n                      {[1, 2, 3, 4].map((i) => (\n                        <div key={i} className=\"animate-pulse\">\n                          <div className=\"flex items-center space-x-3\">\n                            <div className=\"w-10 h-10 bg-gray-200 rounded-full\"></div>\n                            <div className=\"flex-1 space-y-2\">\n                              <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                              <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : recentProfiles.length > 0 ? (\n                    recentProfiles.map((profile) => (\n                      <div key={profile.id} className=\"flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-all duration-200 cursor-pointer\">\n                        <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                          <span className=\"text-white font-bold text-sm\">\n                            {profile.nama.charAt(0).toUpperCase()}\n                          </span>\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"font-medium text-gray-900 truncate\">\n                            {profile.nama}\n                          </p>\n                          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                            <span>{profile.usia} tahun</span>\n                            <span>•</span>\n                            <span>{profile.total_checkups} pemeriksaan</span>\n                          </div>\n                        </div>\n                        <div className=\"text-right flex-shrink-0\">\n                          <p className=\"text-xs text-gray-500\">{formatDate(profile.created_at)}</p>\n                          <div className=\"flex items-center justify-end mt-1\">\n                            <div className=\"w-2 h-2 bg-green-400 rounded-full mr-1\"></div>\n                            <span className=\"text-xs text-green-600\">Aktif</span>\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                        <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"text-gray-500 font-medium\">Belum ada data lansia</p>\n                      <p className=\"text-sm text-gray-400 mt-1\">Mulai dengan mendaftarkan lansia pertama</p>\n                    </div>\n                  )}\n                </div>\n\n                {/* Scroll indicator */}\n                {!isLoading && recentProfiles.length > 4 && (\n                  <div className=\"flex items-center justify-center mt-3 pt-3 border-t border-gray-100\">\n                    <div className=\"flex items-center space-x-2 text-xs text-gray-400\">\n                      <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n                      </svg>\n                      <span>Scroll untuk melihat lebih banyak ({recentProfiles.length - 4} lainnya)</span>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Additional Info Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* System Status */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-bold text-gray-900\">Status Sistem</h3>\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n              </div>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Database</span>\n                  <span className=\"text-sm font-medium text-green-600\">Online</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Server</span>\n                  <span className=\"text-sm font-medium text-green-600\">Aktif</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">Last Backup</span>\n                  <span className=\"text-sm font-medium text-gray-900\">Hari ini</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Tips */}\n            <div className=\"bg-blue-50 rounded-xl shadow-sm border border-blue-100 p-6\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-6 h-6 bg-blue-600 rounded-lg flex items-center justify-center mr-3\">\n                  <svg className=\"w-3 h-3 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-900\">Tips Hari Ini</h3>\n              </div>\n              <p className=\"text-sm text-gray-700 leading-relaxed\">\n                💡 Gunakan fitur <strong>Scan QR Code</strong> untuk akses cepat ke profil lansia.\n                Setiap lansia memiliki QR code unik yang dapat dipindai untuk melihat riwayat kesehatan lengkap.\n              </p>\n            </div>\n          </div>\n        </div>\n      </AdminLayout>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,aAAa;QACb,kBAAkB;QAClB,aAAa;QACb,qBAAqB;IACvB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE9D,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa;YAEb,qCAAqC;YACrC,MAAM,mBAAmB,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAC7C,MAAM,WAAW,iBAAiB,IAAI,CAAC,QAAQ,IAAI,EAAE;YAErD,mBAAmB;YACnB,MAAM,cAAc,SAAS,MAAM;YACnC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,KAAa,UAAiB,MAAM,CAAC,QAAQ,cAAc,IAAI,CAAC,GAAG;YAC7G,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC;gBACnC,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;gBACjD,MAAM,gBAAgB,IAAI;gBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;gBAChD,OAAO,cAAc;YACvB,GAAG,MAAM;YAET,MAAM,eAAe,IAAI,OAAO,QAAQ;YACxC,MAAM,cAAc,IAAI,OAAO,WAAW;YAC1C,MAAM,sBAAsB,SAAS,MAAM,CAAC,CAAC,KAAa;gBACxD,IAAI,QAAQ,YAAY,EAAE;oBACxB,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;oBACjD,IAAI,YAAY,QAAQ,OAAO,gBAAgB,YAAY,WAAW,OAAO,aAAa;wBACxF,OAAO,MAAM;oBACf;gBACF;gBACA,OAAO;YACT,GAAG;YAEH,SAAS;gBACP;gBACA;gBACA;gBACA;YACF;YAEA,gDAAgD;YAChD,kBAAkB;QAEpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,oHAAA,CAAA,SAAM,AAAD;YACX,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,qBACE,6WAAC,oIAAA,CAAA,UAAc;QAAC,cAAc;kBAC5B,cAAA,6WAAC,iIAAA,CAAA,UAAW;YACV,OAAM;YACN,UAAU,CAAC,gBAAgB,EAAE,MAAM,SAAS,GAAG,EAAE,MAAM,eAAe;sBAEtE,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;0DAE7B,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;;4DAAyB;4DAAiB,MAAM;4DAAS;;;;;;;kEACvE,6WAAC;wDAAE,WAAU;;4DAAwB;4DAA8B,MAAM;4DAAc;;;;;;;kEACvF,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;;;;;kFACf,6WAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;wEAAwB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC/E,cAAA,6WAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;kFAEvE,6WAAC;wEAAK,WAAU;kFAAyB,IAAI,OAAO,kBAAkB,CAAC,SAAS;4EAAE,SAAS;4EAAQ,KAAK;4EAAW,OAAO;4EAAQ,MAAM;wEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUhK,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;8DACC,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,0BACC,6WAAC;wDAAI,WAAU;;;;;+DAEf,MAAM,WAAW;;;;;;8DAGrB,6WAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;8DACC,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,0BACC,6WAAC;wDAAI,WAAU;;;;;+DAEf,MAAM,gBAAgB;;;;;;8DAG1B,6WAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;8DACC,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,0BACC,6WAAC;wDAAI,WAAU;;;;;+DAEf,MAAM,WAAW;;;;;;8DAGrB,6WAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;8DACC,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,0BACC,6WAAC;wDAAI,WAAU;;;;;+DAEf,MAAM,mBAAmB;;;;;;8DAG7B,6WAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6WAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAI3E,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2RAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC/E,cAAA,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6WAAC;4DAAK,WAAU;sEAAgD;;;;;;sEAChE,6WAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;8DAG3D,6WAAC,2RAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6WAAC;4DAAK,WAAU;sEAAgD;;;;;;sEAChE,6WAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;8DAG3D,6WAAC,2RAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjF,cAAA,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6WAAC;4DAAK,WAAU;sEAAgD;;;;;;sEAChE,6WAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;8DAG3D,6WAAC,2RAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC/E,cAAA,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6WAAC;4DAAK,WAAU;sEAAgD;;;;;;sEAChE,6WAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjE,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6WAAC,2RAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAIH,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,QAAQ;gDAAQ;0DAExB,0BACC,6WAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6WAAC;4DAAY,WAAU;sEACrB,cAAA,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;;;;;kFACf,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAI,WAAU;;;;;;0FACf,6WAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;2DALX;;;;;;;;;2DAWZ,eAAe,MAAM,GAAG,IAC1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;wDAAqB,WAAU;;0EAC9B,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0EAGvC,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAE,WAAU;kFACV,QAAQ,IAAI;;;;;;kFAEf,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;;oFAAM,QAAQ,IAAI;oFAAC;;;;;;;0FACpB,6WAAC;0FAAK;;;;;;0FACN,6WAAC;;oFAAM,QAAQ,cAAc;oFAAC;;;;;;;;;;;;;;;;;;;0EAGlC,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAE,WAAU;kFAAyB,WAAW,QAAQ,UAAU;;;;;;kFACnE,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAI,WAAU;;;;;;0FACf,6WAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;;;;;;;;uDApBrC,QAAQ,EAAE;;;;8EA0BtB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC/E,cAAA,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6WAAC;4DAAE,WAAU;sEAA4B;;;;;;sEACzC,6WAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;4CAM/C,CAAC,aAAa,eAAe,MAAM,GAAG,mBACrC,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6WAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6WAAC;;gEAAK;gEAAoC,eAAe,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShF,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6WAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6WAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;0DAEvD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6WAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;0DAEvD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6WAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;0CAM1D,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6WAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6WAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;kDAElD,6WAAC;wCAAE,WAAU;;4CAAwC;0DAClC,6WAAC;0DAAO;;;;;;4CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D", "debugId": null}}]}